# XPrinter VIP体系数据库设计

## 1. 数据库设计原则

### 1.1 设计目标
- **可扩展性**：支持未来新增VIP等级和权限类型
- **高性能**：优化查询性能，支持高并发权限检查
- **数据一致性**：确保订阅状态和权限的一致性
- **审计追踪**：完整记录VIP相关的操作历史

### 1.2 核心原则
- 订阅与权限分离设计
- 支持多种计费模式
- 权限配置动态化
- 历史数据完整保留

## 2. 核心数据表设计

### 2.1 VIP套餐表 (vip_plans)

```sql
CREATE TABLE `vip_plans` (
  `plan_id` varchar(50) NOT NULL COMMENT '套餐ID',
  `plan_name` varchar(100) NOT NULL COMMENT '套餐名称',
  `user_tier` varchar(20) NOT NULL COMMENT '用户等级：FREE/VIP_MONTHLY/VIP_YEARLY/VIP_LIFETIME',
  `duration_type` varchar(20) NOT NULL COMMENT '时长类型：MONTHLY/YEARLY/LIFETIME',
  `duration_value` int(11) DEFAULT NULL COMMENT '时长数值（月数，终身版为-1）',
  `price` decimal(10,2) NOT NULL COMMENT '价格',
  `original_price` decimal(10,2) DEFAULT NULL COMMENT '原价（用于显示折扣）',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '货币类型',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有效',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序顺序',
  `description` text COMMENT '套餐描述',
  `features_json` longtext COMMENT '功能列表JSON',
  `metadata_json` longtext COMMENT '扩展元数据JSON',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`plan_id`),
  KEY `idx_user_tier` (`user_tier`),
  KEY `idx_is_active` (`is_active`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP套餐表';
```

### 2.2 用户VIP订阅表 (user_vip_subscriptions)

```sql
CREATE TABLE `user_vip_subscriptions` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '订阅ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `plan_id` varchar(50) NOT NULL COMMENT '套餐ID',
  `user_tier` varchar(20) NOT NULL COMMENT '用户等级',
  `status` varchar(20) NOT NULL COMMENT '订阅状态：ACTIVE/EXPIRED/CANCELLED/SUSPENDED/PENDING',
  `started_at` timestamp NOT NULL COMMENT '开始时间',
  `expires_at` timestamp NULL DEFAULT NULL COMMENT '过期时间（终身版为NULL）',
  `auto_renew` tinyint(1) DEFAULT 0 COMMENT '是否自动续费',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
  `order_id` varchar(100) DEFAULT NULL COMMENT '订单ID',
  `original_order_id` varchar(100) DEFAULT NULL COMMENT '原始订单ID（续费时使用）',
  `trial_days` int(11) DEFAULT 0 COMMENT '试用天数',
  `is_trial` tinyint(1) DEFAULT 0 COMMENT '是否为试用',
  `cancelled_at` timestamp NULL DEFAULT NULL COMMENT '取消时间',
  `cancel_reason` varchar(500) DEFAULT NULL COMMENT '取消原因',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_active` (`user_id`, `status`) USING BTREE,
  KEY `idx_user_id` (`user_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_status` (`status`),
  KEY `idx_expires_at` (`expires_at`),
  CONSTRAINT `fk_subscription_plan` FOREIGN KEY (`plan_id`) REFERENCES `vip_plans` (`plan_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户VIP订阅表';
```

### 2.3 VIP权限配置表 (vip_permission_configs)

```sql
CREATE TABLE `vip_permission_configs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `user_tier` varchar(20) NOT NULL COMMENT '用户等级',
  `permission_type` varchar(50) NOT NULL COMMENT '权限类型',
  `permission_category` varchar(20) NOT NULL COMMENT '权限分类：feature/quota/experience',
  `is_enabled` tinyint(1) NOT NULL DEFAULT 0 COMMENT '是否启用',
  `permission_value` varchar(200) DEFAULT NULL COMMENT '权限值（配额类权限的具体数值）',
  `metadata_json` longtext COMMENT '权限元数据JSON',
  `effective_date` timestamp NULL DEFAULT NULL COMMENT '生效时间',
  `expiry_date` timestamp NULL DEFAULT NULL COMMENT '失效时间',
  `is_active` tinyint(1) NOT NULL DEFAULT 1 COMMENT '是否有效',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tier_permission` (`user_tier`, `permission_type`),
  KEY `idx_user_tier` (`user_tier`),
  KEY `idx_permission_type` (`permission_type`),
  KEY `idx_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP权限配置表';
```

### 2.4 用户权限使用记录表 (user_permission_usage)

```sql
CREATE TABLE `user_permission_usage` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '使用记录ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `permission_type` varchar(50) NOT NULL COMMENT '权限类型',
  `usage_date` date NOT NULL COMMENT '使用日期',
  `usage_count` int(11) NOT NULL DEFAULT 0 COMMENT '使用次数',
  `usage_amount` bigint(20) DEFAULT 0 COMMENT '使用量（如存储字节数）',
  `quota_limit` int(11) DEFAULT NULL COMMENT '配额限制',
  `reset_cycle` varchar(20) DEFAULT 'MONTHLY' COMMENT '重置周期：DAILY/MONTHLY/YEARLY',
  `last_reset_at` timestamp NULL DEFAULT NULL COMMENT '上次重置时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_permission_date` (`user_id`, `permission_type`, `usage_date`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_permission_type` (`permission_type`),
  KEY `idx_usage_date` (`usage_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户权限使用记录表';
```

### 2.5 VIP订单表 (vip_orders)

```sql
CREATE TABLE `vip_orders` (
  `order_id` varchar(100) NOT NULL COMMENT '订单ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `plan_id` varchar(50) NOT NULL COMMENT '套餐ID',
  `order_type` varchar(20) NOT NULL COMMENT '订单类型：NEW/RENEW/UPGRADE/DOWNGRADE',
  `amount` decimal(10,2) NOT NULL COMMENT '订单金额',
  `currency` varchar(10) NOT NULL DEFAULT 'CNY' COMMENT '货币类型',
  `payment_method` varchar(50) DEFAULT NULL COMMENT '支付方式',
  `payment_status` varchar(20) NOT NULL COMMENT '支付状态：PENDING/PAID/FAILED/REFUNDED',
  `payment_time` timestamp NULL DEFAULT NULL COMMENT '支付时间',
  `payment_transaction_id` varchar(200) DEFAULT NULL COMMENT '支付交易ID',
  `discount_amount` decimal(10,2) DEFAULT 0.00 COMMENT '折扣金额',
  `coupon_code` varchar(50) DEFAULT NULL COMMENT '优惠券代码',
  `invoice_required` tinyint(1) DEFAULT 0 COMMENT '是否需要发票',
  `invoice_info_json` longtext COMMENT '发票信息JSON',
  `refund_amount` decimal(10,2) DEFAULT 0.00 COMMENT '退款金额',
  `refund_reason` varchar(500) DEFAULT NULL COMMENT '退款原因',
  `refund_time` timestamp NULL DEFAULT NULL COMMENT '退款时间',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`order_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_plan_id` (`plan_id`),
  KEY `idx_payment_status` (`payment_status`),
  KEY `idx_payment_time` (`payment_time`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP订单表';
```

### 2.6 VIP操作日志表 (vip_operation_logs)

```sql
CREATE TABLE `vip_operation_logs` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `user_id` bigint(20) NOT NULL COMMENT '用户ID',
  `operation_type` varchar(50) NOT NULL COMMENT '操作类型：SUBSCRIBE/RENEW/CANCEL/UPGRADE/DOWNGRADE/PERMISSION_CHECK',
  `operation_details` longtext COMMENT '操作详情JSON',
  `old_tier` varchar(20) DEFAULT NULL COMMENT '操作前等级',
  `new_tier` varchar(20) DEFAULT NULL COMMENT '操作后等级',
  `order_id` varchar(100) DEFAULT NULL COMMENT '关联订单ID',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` varchar(500) DEFAULT NULL COMMENT '用户代理',
  `result` varchar(20) NOT NULL COMMENT '操作结果：SUCCESS/FAILED',
  `error_message` varchar(1000) DEFAULT NULL COMMENT '错误信息',
  `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_operation_type` (`operation_type`),
  KEY `idx_created_at` (`created_at`),
  KEY `idx_result` (`result`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='VIP操作日志表';
```

## 3. 初始化数据脚本

### 3.1 VIP套餐初始化

```sql
-- 插入VIP套餐数据
INSERT INTO `vip_plans` VALUES 
('free_plan', '免费版', 'FREE', 'UNLIMITED', -1, 0.00, 0.00, 'CNY', 1, 0, '基础功能，适合个人用户', 
 '{"templates": 100, "storage": "1GB", "data_recovery": false}', '{}', NOW(), NOW()),

('vip_monthly', 'VIP月度版', 'VIP_MONTHLY', 'MONTHLY', 1, 19.90, 29.90, 'CNY', 1, 1, '解锁全部功能，月度订阅', 
 '{"templates": "unlimited", "storage": "10GB", "data_recovery": true, "ocr": true}', '{}', NOW(), NOW()),

('vip_yearly', 'VIP年度版', 'VIP_YEARLY', 'YEARLY', 12, 199.00, 358.80, 'CNY', 1, 2, '解锁全部功能，年度优惠', 
 '{"templates": "unlimited", "storage": "50GB", "data_recovery": true, "ocr": true, "team": 20}', '{}', NOW(), NOW()),

('vip_lifetime', 'VIP终身版', 'VIP_LIFETIME', 'LIFETIME', -1, 999.00, 1999.00, 'CNY', 1, 3, '一次购买，终身享受',
 '{"templates": "unlimited", "storage": "100GB", "data_recovery": true, "ocr": true, "team": 50}', '{}', NOW(), NOW());
```

### 3.2 VIP权限配置初始化

```sql
-- 免费用户权限配置
INSERT INTO `vip_permission_configs` VALUES
(1, 'FREE', 'FEATURE_DATA_RECOVERY', 'feature', 0, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(2, 'FREE', 'FEATURE_OCR_CREATE', 'feature', 0, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(3, 'FREE', 'FEATURE_PRICE_TAG', 'feature', 0, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(4, 'FREE', 'FEATURE_TEAM_COLLABORATION', 'feature', 0, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(5, 'FREE', 'QUOTA_TEMPLATE_COUNT', 'quota', 1, '100', '{}', NULL, NULL, 1, NOW(), NOW()),
(6, 'FREE', 'QUOTA_STORAGE_SIZE', 'quota', 1, '1073741824', '{"unit": "bytes", "display": "1GB"}', NULL, NULL, 1, NOW(), NOW()),
(7, 'FREE', 'EXPERIENCE_AD_FREE', 'experience', 0, NULL, '{}', NULL, NULL, 1, NOW(), NOW());

-- VIP月度用户权限配置
INSERT INTO `vip_permission_configs` VALUES
(11, 'VIP_MONTHLY', 'FEATURE_DATA_RECOVERY', 'feature', 1, '6', '{"period": "months"}', NULL, NULL, 1, NOW(), NOW()),
(12, 'VIP_MONTHLY', 'FEATURE_OCR_CREATE', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(13, 'VIP_MONTHLY', 'FEATURE_PRICE_TAG', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(14, 'VIP_MONTHLY', 'FEATURE_TEAM_COLLABORATION', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(15, 'VIP_MONTHLY', 'QUOTA_TEMPLATE_COUNT', 'quota', 1, '-1', '{"unlimited": true}', NULL, NULL, 1, NOW(), NOW()),
(16, 'VIP_MONTHLY', 'QUOTA_STORAGE_SIZE', 'quota', 1, '10737418240', '{"unit": "bytes", "display": "10GB"}', NULL, NULL, 1, NOW(), NOW()),
(17, 'VIP_MONTHLY', 'QUOTA_TEAM_MEMBERS', 'quota', 1, '5', '{}', NULL, NULL, 1, NOW(), NOW()),
(18, 'VIP_MONTHLY', 'QUOTA_OCR_MONTHLY', 'quota', 1, '1000', '{"reset_cycle": "monthly"}', NULL, NULL, 1, NOW(), NOW()),
(19, 'VIP_MONTHLY', 'EXPERIENCE_AD_FREE', 'experience', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW());

-- VIP年度用户权限配置
INSERT INTO `vip_permission_configs` VALUES
(21, 'VIP_YEARLY', 'FEATURE_DATA_RECOVERY', 'feature', 1, '6', '{"period": "months"}', NULL, NULL, 1, NOW(), NOW()),
(22, 'VIP_YEARLY', 'FEATURE_OCR_CREATE', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(23, 'VIP_YEARLY', 'FEATURE_PRICE_TAG', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(24, 'VIP_YEARLY', 'FEATURE_TEAM_COLLABORATION', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(25, 'VIP_YEARLY', 'QUOTA_TEMPLATE_COUNT', 'quota', 1, '-1', '{"unlimited": true}', NULL, NULL, 1, NOW(), NOW()),
(26, 'VIP_YEARLY', 'QUOTA_STORAGE_SIZE', 'quota', 1, '53687091200', '{"unit": "bytes", "display": "50GB"}', NULL, NULL, 1, NOW(), NOW()),
(27, 'VIP_YEARLY', 'QUOTA_TEAM_MEMBERS', 'quota', 1, '20', '{}', NULL, NULL, 1, NOW(), NOW()),
(28, 'VIP_YEARLY', 'QUOTA_OCR_MONTHLY', 'quota', 1, '5000', '{"reset_cycle": "monthly"}', NULL, NULL, 1, NOW(), NOW()),
(29, 'VIP_YEARLY', 'EXPERIENCE_AD_FREE', 'experience', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(30, 'VIP_YEARLY', 'EXPERIENCE_BETA_FEATURES', 'experience', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW());

-- VIP终身用户权限配置
INSERT INTO `vip_permission_configs` VALUES
(31, 'VIP_LIFETIME', 'FEATURE_DATA_RECOVERY', 'feature', 1, '6', '{"period": "months"}', NULL, NULL, 1, NOW(), NOW()),
(32, 'VIP_LIFETIME', 'FEATURE_OCR_CREATE', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(33, 'VIP_LIFETIME', 'FEATURE_PRICE_TAG', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(34, 'VIP_LIFETIME', 'FEATURE_TEAM_COLLABORATION', 'feature', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(35, 'VIP_LIFETIME', 'QUOTA_TEMPLATE_COUNT', 'quota', 1, '-1', '{"unlimited": true}', NULL, NULL, 1, NOW(), NOW()),
(36, 'VIP_LIFETIME', 'QUOTA_STORAGE_SIZE', 'quota', 1, '107374182400', '{"unit": "bytes", "display": "100GB"}', NULL, NULL, 1, NOW(), NOW()),
(37, 'VIP_LIFETIME', 'QUOTA_TEAM_MEMBERS', 'quota', 1, '50', '{}', NULL, NULL, 1, NOW(), NOW()),
(38, 'VIP_LIFETIME', 'QUOTA_OCR_MONTHLY', 'quota', 1, '-1', '{"unlimited": true}', NULL, NULL, 1, NOW(), NOW()),
(39, 'VIP_LIFETIME', 'EXPERIENCE_AD_FREE', 'experience', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(40, 'VIP_LIFETIME', 'EXPERIENCE_BETA_FEATURES', 'experience', 1, NULL, '{}', NULL, NULL, 1, NOW(), NOW()),
(41, 'VIP_LIFETIME', 'EXPERIENCE_PRIORITY_SUPPORT', 'experience', 1, 'highest', '{}', NULL, NULL, 1, NOW(), NOW());
```

## 4. 索引优化策略

### 4.1 查询优化索引

```sql
-- 用户订阅状态查询优化
CREATE INDEX idx_user_subscription_status ON user_vip_subscriptions(user_id, status, expires_at);

-- 权限检查查询优化
CREATE INDEX idx_permission_tier_type ON vip_permission_configs(user_tier, permission_type, is_active);

-- 使用记录查询优化
CREATE INDEX idx_usage_user_date ON user_permission_usage(user_id, permission_type, usage_date);

-- 订单查询优化
CREATE INDEX idx_order_user_status ON vip_orders(user_id, payment_status, created_at);

-- 操作日志查询优化
CREATE INDEX idx_log_user_operation ON vip_operation_logs(user_id, operation_type, created_at);
```

### 4.2 分区策略（可选）

```sql
-- 按月分区操作日志表（数据量大时使用）
ALTER TABLE vip_operation_logs
PARTITION BY RANGE (YEAR(created_at)*100 + MONTH(created_at)) (
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    -- ... 继续添加分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);

-- 按月分区使用记录表
ALTER TABLE user_permission_usage
PARTITION BY RANGE (YEAR(usage_date)*100 + MONTH(usage_date)) (
    PARTITION p202407 VALUES LESS THAN (202408),
    PARTITION p202408 VALUES LESS THAN (202409),
    -- ... 继续添加分区
    PARTITION p_future VALUES LESS THAN MAXVALUE
);
```

## 5. 数据一致性保证

### 5.1 事务处理

```sql
-- 用户订阅激活事务示例
DELIMITER //
CREATE PROCEDURE ActivateVipSubscription(
    IN p_user_id BIGINT,
    IN p_plan_id VARCHAR(50),
    IN p_order_id VARCHAR(100)
)
BEGIN
    DECLARE EXIT HANDLER FOR SQLEXCEPTION
    BEGIN
        ROLLBACK;
        RESIGNAL;
    END;

    START TRANSACTION;

    -- 1. 更新用户表
    UPDATE users SET user_tier = (
        SELECT user_tier FROM vip_plans WHERE plan_id = p_plan_id
    ) WHERE id = p_user_id;

    -- 2. 插入订阅记录
    INSERT INTO user_vip_subscriptions (
        user_id, plan_id, user_tier, status, started_at, expires_at
    ) VALUES (
        p_user_id,
        p_plan_id,
        (SELECT user_tier FROM vip_plans WHERE plan_id = p_plan_id),
        'ACTIVE',
        NOW(),
        CASE
            WHEN (SELECT duration_value FROM vip_plans WHERE plan_id = p_plan_id) = -1
            THEN NULL
            ELSE DATE_ADD(NOW(), INTERVAL (SELECT duration_value FROM vip_plans WHERE plan_id = p_plan_id) MONTH)
        END
    );

    -- 3. 更新订单状态
    UPDATE vip_orders SET payment_status = 'PAID', payment_time = NOW()
    WHERE order_id = p_order_id;

    -- 4. 记录操作日志
    INSERT INTO vip_operation_logs (user_id, operation_type, operation_details, new_tier, order_id, result)
    VALUES (p_user_id, 'SUBSCRIBE', JSON_OBJECT('plan_id', p_plan_id),
            (SELECT user_tier FROM vip_plans WHERE plan_id = p_plan_id), p_order_id, 'SUCCESS');

    COMMIT;
END //
DELIMITER ;
```

### 5.2 数据完整性约束

```sql
-- 添加检查约束
ALTER TABLE user_vip_subscriptions
ADD CONSTRAINT chk_subscription_dates
CHECK (expires_at IS NULL OR expires_at > started_at);

ALTER TABLE vip_orders
ADD CONSTRAINT chk_order_amount
CHECK (amount >= 0);

ALTER TABLE user_permission_usage
ADD CONSTRAINT chk_usage_count
CHECK (usage_count >= 0);
```

## 6. 性能监控

### 6.1 关键指标监控

```sql
-- 创建监控视图
CREATE VIEW vip_metrics_daily AS
SELECT
    DATE(created_at) as metric_date,
    COUNT(*) as new_subscriptions,
    SUM(CASE WHEN user_tier = 'VIP_MONTHLY' THEN 1 ELSE 0 END) as monthly_subs,
    SUM(CASE WHEN user_tier = 'VIP_YEARLY' THEN 1 ELSE 0 END) as yearly_subs,
    SUM(CASE WHEN user_tier = 'VIP_LIFETIME' THEN 1 ELSE 0 END) as lifetime_subs
FROM user_vip_subscriptions
WHERE status = 'ACTIVE'
GROUP BY DATE(created_at);

-- 权限使用统计视图
CREATE VIEW permission_usage_stats AS
SELECT
    permission_type,
    usage_date,
    COUNT(DISTINCT user_id) as active_users,
    SUM(usage_count) as total_usage,
    AVG(usage_count) as avg_usage_per_user
FROM user_permission_usage
GROUP BY permission_type, usage_date;
```
```
