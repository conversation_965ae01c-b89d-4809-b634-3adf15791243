# 真实6个月功能验证测试

## 🧪 快速验证脚本

### 1. 数据库准备
```sql
-- 确保用户表有VIP等级字段
ALTER TABLE `user` ADD COLUMN `user_tier` VARCHAR(20) NOT NULL DEFAULT 'FREE' COMMENT 'VIP等级：FREE/VIP_MONTHLY/VIP_YEARLY/VIP_LIFETIME';

-- 创建测试用户
UPDATE user SET user_tier = 'FREE' WHERE userId = 1;
UPDATE user SET user_tier = 'VIP_MONTHLY' WHERE userId = 2;
```

### 2. 创建测试数据
```sql
-- 为免费用户创建测试数据
-- 5天前删除（应该可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (1, '免费用户-5天前删除', '{}', DATE_SUB(NOW(), INTERVAL 5 DAY), DATE_SUB(NOW(), INTERVAL 15 DAY), DATE_SUB(NOW(), INTERVAL 5 DAY));

-- 10天前删除（应该不可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (1, '免费用户-10天前删除', '{}', DATE_SUB(NOW(), INTERVAL 10 DAY), DATE_SUB(NOW(), INTERVAL 20 DAY), DATE_SUB(NOW(), INTERVAL 10 DAY));

-- 为VIP用户创建测试数据
-- 真实6个月前删除（应该可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (2, 'VIP用户-真实6个月前删除', '{}', DATE_SUB(NOW(), INTERVAL 6 MONTH), DATE_SUB(NOW(), INTERVAL 7 MONTH), DATE_SUB(NOW(), INTERVAL 6 MONTH));

-- 7个月前删除（应该不可恢复）
INSERT INTO templet (userId, name, data, deleteTime, createTime, updateTime) 
VALUES (2, 'VIP用户-7个月前删除', '{}', DATE_SUB(NOW(), INTERVAL 7 MONTH), DATE_SUB(NOW(), INTERVAL 8 MONTH), DATE_SUB(NOW(), INTERVAL 7 MONTH));
```

### 3. 验证查询结果
```sql
-- 查看测试数据的时间分布
SELECT 
    userId,
    name,
    deleteTime,
    DATEDIFF(NOW(), deleteTime) as days_ago,
    CASE 
        WHEN userId = 1 AND DATEDIFF(NOW(), deleteTime) <= 7 THEN '免费用户可恢复'
        WHEN userId = 2 AND deleteTime >= DATE_SUB(NOW(), INTERVAL 6 MONTH) THEN 'VIP用户可恢复'
        ELSE '不可恢复'
    END as recovery_status
FROM templet 
WHERE userId IN (1, 2) AND deleteTime IS NOT NULL
ORDER BY userId, deleteTime DESC;

-- 验证真实6个月的计算
SELECT 
    NOW() as current_time,
    DATE_SUB(NOW(), INTERVAL 6 MONTH) as six_months_ago,
    DATEDIFF(NOW(), DATE_SUB(NOW(), INTERVAL 6 MONTH)) as actual_days,
    180 as fixed_days,
    DATEDIFF(NOW(), DATE_SUB(NOW(), INTERVAL 6 MONTH)) - 180 as difference
;
```

## 📱 API测试

### 1. 测试权限查询接口

**免费用户权限查询：**
```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getRecoveryPermission" \
  -H "Authorization: Bearer [免费用户token]" \
  -H "Content-Type: application/json"
```

**预期响应（注意recoveryPeriodDays应该是7）：**
```json
{
  "success": true,
  "data": {
    "hasPermission": true,
    "userTier": "FREE",
    "userTierDisplay": "免费用户",
    "recoveryPeriodDays": 7,
    "earliestRecoveryDate": "2024-07-15T00:00:00",
    "upgradeMessage": "升级VIP可恢复6个月内的数据"
  }
}
```

**VIP用户权限查询：**
```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getRecoveryPermission" \
  -H "Authorization: Bearer [VIP用户token]" \
  -H "Content-Type: application/json"
```

**预期响应（注意recoveryPeriodDays应该是181-184之间的动态值）：**
```json
{
  "success": true,
  "data": {
    "hasPermission": true,
    "userTier": "VIP_MONTHLY",
    "userTierDisplay": "VIP月度",
    "recoveryPeriodDays": 182,
    "earliestRecoveryDate": "2024-01-22T00:00:00",
    "upgradeMessage": null
  }
}
```

### 2. 测试数据查询接口

**免费用户查询已删除数据：**
```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getDeletedData?dataType=template&pageNumber=1&pageSize=10" \
  -H "Authorization: Bearer [免费用户token]" \
  -H "Content-Type: application/json"
```

**验证点：**
- 只应该返回7天内删除的数据
- 10天前删除的数据不应该出现

**VIP用户查询已删除数据：**
```bash
curl -X GET "http://localhost:8080/api/v2/datarecovery/getDeletedData?dataType=template&pageNumber=1&pageSize=10" \
  -H "Authorization: Bearer [VIP用户token]" \
  -H "Content-Type: application/json"
```

**验证点：**
- 应该返回真实6个月内删除的数据
- 7个月前删除的数据不应该出现
- 6个月前删除的数据应该出现

## 🔍 关键验证点

### 1. 时间计算准确性
```java
// 可以在代码中添加日志验证
Calendar calendar = Calendar.getInstance();
System.out.println("当前时间: " + calendar.getTime());

calendar.add(Calendar.MONTH, -6);
System.out.println("6个月前: " + calendar.getTime());

long diffInMillis = System.currentTimeMillis() - calendar.getTimeInMillis();
int actualDays = (int) (diffInMillis / (24 * 60 * 60 * 1000));
System.out.println("实际天数: " + actualDays);
```

### 2. 边界条件测试
- 测试恰好6个月边界的数据
- 测试不同月份的天数差异
- 测试闰年2月的情况

### 3. 性能验证
- 确认动态计算不会显著影响性能
- 验证大量数据查询的响应时间

## 📊 预期结果对比

### 当前时间：2024年7月22日

| 用户类型 | 可恢复到 | 实际天数 | 之前固定天数 | 差异 |
|----------|----------|----------|--------------|------|
| 免费用户 | 2024-07-15 | 7天 | 7天 | 无变化 |
| VIP用户 | 2024-01-22 | ~182天 | 180天 | +2天 |

### 不同时间点的VIP用户天数变化

| 查询时间 | 6个月前 | 实际天数 | 说明 |
|----------|---------|----------|------|
| 2024-01-31 | 2023-07-31 | ~184天 | 包含31天月份较多 |
| 2024-02-29 | 2023-08-29 | ~184天 | 闰年2月 |
| 2024-03-01 | 2023-09-01 | ~181天 | 包含2月 |
| 2024-07-22 | 2024-01-22 | ~182天 | 正常情况 |

## ✅ 验证清单

### 数据库层面
- [ ] user_tier字段存在且有正确的测试数据
- [ ] 测试数据覆盖各种时间边界
- [ ] SQL查询返回预期的数据集

### API层面
- [ ] 权限查询接口返回动态计算的天数
- [ ] 数据查询接口正确过滤时间范围
- [ ] 恢复接口正确验证时间权限

### 业务逻辑层面
- [ ] 免费用户仍然是7天限制
- [ ] VIP用户使用真实6个月计算
- [ ] 边界数据处理正确

### 用户体验层面
- [ ] 响应时间无明显增加
- [ ] 错误提示清晰准确
- [ ] 升级引导信息正确

## 🐛 可能的问题和解决方案

### 1. 时区问题
**问题**：不同时区的用户可能看到不同的结果
**解决**：确保服务器时区设置正确，或考虑用户时区

### 2. 性能问题
**问题**：动态计算可能影响性能
**解决**：如果需要，可以考虑缓存计算结果

### 3. 边界精度问题
**问题**：毫秒级的时间差可能导致边界判断不准确
**解决**：在时间比较时使用适当的精度处理

## 🎯 成功标准

1. **功能正确性**：VIP用户确实可以恢复真实6个月内的数据
2. **时间准确性**：计算的天数与实际日历月份一致
3. **性能稳定**：响应时间无明显增加
4. **向后兼容**：现有功能不受影响
5. **用户体验**：界面显示更符合用户预期
