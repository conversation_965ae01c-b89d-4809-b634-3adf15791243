# XPrinter VIP订阅与计费系统

## 1. 订阅系统架构

### 1.1 设计目标
- **灵活计费**：支持多种订阅模式和计费周期
- **自动续费**：支持自动续费和手动续费
- **优雅降级**：VIP过期后的平滑降级处理
- **订单管理**：完整的订单生命周期管理

### 1.2 核心组件
```
VIP订阅与计费系统
├── 订阅管理模块 (Subscription Management)
├── 计费引擎 (Billing Engine)
├── 支付集成 (Payment Integration)
├── 续费管理 (Renewal Management)
└── 降级处理 (Downgrade Handler)
```

## 2. 订阅管理服务

### 2.1 订阅服务核心实现

```java
/**
 * VIP订阅管理服务
 */
@Service
@Transactional
public class VipSubscriptionService {
    
    @Autowired
    private UserVipSubscriptionMapper subscriptionMapper;
    
    @Autowired
    private VipOrderService orderService;
    
    @Autowired
    private VipPermissionService permissionService;
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 创建新订阅
     */
    public SubscriptionResult createSubscription(CreateSubscriptionRequest request) {
        try {
            // 1. 验证用户和套餐
            User user = userService.getUserById(request.getUserId());
            VipPlan plan = vipPlanService.getPlanById(request.getPlanId());
            
            if (user == null || plan == null || !plan.getIsActive()) {
                return SubscriptionResult.failure("用户或套餐不存在");
            }
            
            // 2. 检查是否已有活跃订阅
            UserVipSubscription existingSubscription = getActiveSubscription(request.getUserId());
            if (existingSubscription != null) {
                return handleUpgradeOrRenewal(existingSubscription, plan, request);
            }
            
            // 3. 创建订单
            VipOrder order = orderService.createOrder(request.getUserId(), plan, OrderType.NEW);
            
            // 4. 创建订阅记录（待激活状态）
            UserVipSubscription subscription = new UserVipSubscription();
            subscription.setUserId(request.getUserId());
            subscription.setPlanId(plan.getPlanId());
            subscription.setUserTier(plan.getUserTier());
            subscription.setStatus(SubscriptionStatus.PENDING);
            subscription.setStartedAt(LocalDateTime.now());
            subscription.setExpiresAt(calculateExpiryDate(plan));
            subscription.setAutoRenew(request.isAutoRenew());
            subscription.setPaymentMethod(request.getPaymentMethod());
            subscription.setOrderId(order.getOrderId());
            subscription.setCreatedAt(LocalDateTime.now());
            subscription.setUpdatedAt(LocalDateTime.now());
            
            subscriptionMapper.insert(subscription);
            
            return SubscriptionResult.success(subscription, order);
            
        } catch (Exception e) {
            log.error("创建订阅失败, request: {}", request, e);
            return SubscriptionResult.failure("创建订阅失败: " + e.getMessage());
        }
    }
    
    /**
     * 激活订阅（支付成功后调用）
     */
    public void activateSubscription(String orderId) {
        try {
            // 1. 查找订阅记录
            UserVipSubscription subscription = subscriptionMapper.selectByOrderId(orderId);
            if (subscription == null) {
                throw new IllegalArgumentException("订阅记录不存在");
            }
            
            // 2. 更新订阅状态
            subscription.setStatus(SubscriptionStatus.ACTIVE);
            subscription.setUpdatedAt(LocalDateTime.now());
            subscriptionMapper.updateById(subscription);
            
            // 3. 更新用户等级
            userService.updateUserTier(subscription.getUserId(), subscription.getUserTier());
            
            // 4. 清除权限缓存
            permissionService.clearUserPermissionCache(subscription.getUserId());
            
            // 5. 发布订阅激活事件
            VipSubscriptionActivatedEvent event = VipSubscriptionActivatedEvent.builder()
                .userId(subscription.getUserId())
                .userTier(subscription.getUserTier())
                .planId(subscription.getPlanId())
                .activatedAt(LocalDateTime.now())
                .expiresAt(subscription.getExpiresAt())
                .build();
            
            eventPublisher.publishEvent(event);
            
            log.info("VIP订阅激活成功, userId: {}, tier: {}", 
                subscription.getUserId(), subscription.getUserTier());
            
        } catch (Exception e) {
            log.error("激活订阅失败, orderId: {}", orderId, e);
            throw new SubscriptionException("激活订阅失败", e);
        }
    }
    
    /**
     * 续费订阅
     */
    public SubscriptionResult renewSubscription(Long userId, String planId, boolean autoRenew) {
        try {
            // 1. 获取当前订阅
            UserVipSubscription currentSubscription = getActiveSubscription(userId);
            if (currentSubscription == null) {
                return SubscriptionResult.failure("无活跃订阅");
            }
            
            // 2. 获取续费套餐
            VipPlan plan = vipPlanService.getPlanById(planId);
            if (plan == null || !plan.getIsActive()) {
                return SubscriptionResult.failure("套餐不存在或已下架");
            }
            
            // 3. 创建续费订单
            VipOrder renewalOrder = orderService.createOrder(userId, plan, OrderType.RENEW);
            renewalOrder.setOriginalOrderId(currentSubscription.getOrderId());
            
            // 4. 计算新的过期时间
            LocalDateTime newExpiryDate = calculateRenewalExpiryDate(currentSubscription, plan);
            
            // 5. 更新订阅信息
            currentSubscription.setPlanId(planId);
            currentSubscription.setUserTier(plan.getUserTier());
            currentSubscription.setExpiresAt(newExpiryDate);
            currentSubscription.setAutoRenew(autoRenew);
            currentSubscription.setUpdatedAt(LocalDateTime.now());
            subscriptionMapper.updateById(currentSubscription);
            
            return SubscriptionResult.success(currentSubscription, renewalOrder);
            
        } catch (Exception e) {
            log.error("续费订阅失败, userId: {}, planId: {}", userId, planId, e);
            return SubscriptionResult.failure("续费失败: " + e.getMessage());
        }
    }
    
    /**
     * 取消订阅
     */
    public void cancelSubscription(Long userId, String cancelReason) {
        try {
            UserVipSubscription subscription = getActiveSubscription(userId);
            if (subscription == null) {
                throw new IllegalArgumentException("无活跃订阅");
            }
            
            // 1. 更新订阅状态
            subscription.setStatus(SubscriptionStatus.CANCELLED);
            subscription.setCancelledAt(LocalDateTime.now());
            subscription.setCancelReason(cancelReason);
            subscription.setAutoRenew(false);
            subscription.setUpdatedAt(LocalDateTime.now());
            subscriptionMapper.updateById(subscription);
            
            // 2. 立即降级用户
            handleSubscriptionDowngrade(subscription);
            
            // 3. 发布取消事件
            VipSubscriptionCancelledEvent event = VipSubscriptionCancelledEvent.builder()
                .userId(userId)
                .previousTier(subscription.getUserTier())
                .cancelledAt(LocalDateTime.now())
                .cancelReason(cancelReason)
                .build();
            
            eventPublisher.publishEvent(event);
            
            log.info("VIP订阅取消成功, userId: {}, reason: {}", userId, cancelReason);
            
        } catch (Exception e) {
            log.error("取消订阅失败, userId: {}", userId, e);
            throw new SubscriptionException("取消订阅失败", e);
        }
    }
    
    /**
     * 获取用户活跃订阅
     */
    public UserVipSubscription getActiveSubscription(Long userId) {
        return subscriptionMapper.selectActiveByUserId(userId);
    }
    
    /**
     * 计算过期时间
     */
    private LocalDateTime calculateExpiryDate(VipPlan plan) {
        if (plan.getDurationType().equals("LIFETIME")) {
            return null; // 终身版无过期时间
        }
        
        return LocalDateTime.now().plusMonths(plan.getDurationValue());
    }
    
    /**
     * 计算续费后的过期时间
     */
    private LocalDateTime calculateRenewalExpiryDate(UserVipSubscription currentSubscription, VipPlan plan) {
        if (plan.getDurationType().equals("LIFETIME")) {
            return null; // 升级到终身版
        }
        
        LocalDateTime baseDate = currentSubscription.getExpiresAt();
        if (baseDate == null || baseDate.isBefore(LocalDateTime.now())) {
            baseDate = LocalDateTime.now(); // 如果已过期，从当前时间开始计算
        }
        
        return baseDate.plusMonths(plan.getDurationValue());
    }
    
    /**
     * 处理订阅降级
     */
    private void handleSubscriptionDowngrade(UserVipSubscription subscription) {
        // 1. 更新用户等级为免费用户
        userService.updateUserTier(subscription.getUserId(), UserTier.FREE);
        
        // 2. 清除权限缓存
        permissionService.clearUserPermissionCache(subscription.getUserId());
        
        // 3. 处理数据降级（如果需要）
        handleDataDowngrade(subscription.getUserId(), subscription.getUserTier());
    }
    
    /**
     * 处理数据降级
     */
    private void handleDataDowngrade(Long userId, UserTier previousTier) {
        // 这里可以添加数据降级逻辑，比如：
        // - 检查模板数量是否超出免费用户限制
        // - 处理存储空间超限问题
        // - 禁用VIP专属功能
        
        // 示例：检查模板数量
        int templateCount = templateService.getUserTemplateCount(userId);
        int freeUserLimit = 100;
        
        if (templateCount > freeUserLimit) {
            // 发送通知提醒用户处理超限模板
            notificationService.sendTemplateOverLimitNotification(userId, templateCount, freeUserLimit);
        }
    }
}
```

## 3. 计费引擎

### 3.1 订单管理服务

```java
/**
 * VIP订单服务
 */
@Service
public class VipOrderService {
    
    @Autowired
    private VipOrderMapper orderMapper;
    
    @Autowired
    private VipPlanService planService;
    
    @Autowired
    private CouponService couponService;
    
    /**
     * 创建订单
     */
    public VipOrder createOrder(Long userId, VipPlan plan, OrderType orderType) {
        String orderId = generateOrderId();
        
        VipOrder order = new VipOrder();
        order.setOrderId(orderId);
        order.setUserId(userId);
        order.setPlanId(plan.getPlanId());
        order.setOrderType(orderType.name());
        order.setAmount(plan.getPrice());
        order.setCurrency(plan.getCurrency());
        order.setPaymentStatus(PaymentStatus.PENDING.name());
        order.setDiscountAmount(BigDecimal.ZERO);
        order.setRefundAmount(BigDecimal.ZERO);
        order.setCreatedAt(LocalDateTime.now());
        order.setUpdatedAt(LocalDateTime.now());
        
        orderMapper.insert(order);
        return order;
    }
    
    /**
     * 应用优惠券
     */
    public OrderDiscountResult applyCoupon(String orderId, String couponCode) {
        try {
            VipOrder order = orderMapper.selectById(orderId);
            if (order == null) {
                return OrderDiscountResult.failure("订单不存在");
            }
            
            if (!PaymentStatus.PENDING.name().equals(order.getPaymentStatus())) {
                return OrderDiscountResult.failure("订单状态不允许使用优惠券");
            }
            
            // 验证优惠券
            CouponValidationResult couponResult = couponService.validateCoupon(
                couponCode, order.getUserId(), order.getPlanId());
            
            if (!couponResult.isValid()) {
                return OrderDiscountResult.failure(couponResult.getMessage());
            }
            
            // 计算折扣金额
            BigDecimal discountAmount = calculateDiscount(order.getAmount(), couponResult.getCoupon());
            BigDecimal finalAmount = order.getAmount().subtract(discountAmount);
            
            // 更新订单
            order.setCouponCode(couponCode);
            order.setDiscountAmount(discountAmount);
            order.setAmount(finalAmount);
            order.setUpdatedAt(LocalDateTime.now());
            orderMapper.updateById(order);
            
            // 标记优惠券为已使用
            couponService.useCoupon(couponCode, orderId);
            
            return OrderDiscountResult.success(discountAmount, finalAmount);
            
        } catch (Exception e) {
            log.error("应用优惠券失败, orderId: {}, couponCode: {}", orderId, couponCode, e);
            return OrderDiscountResult.failure("应用优惠券失败");
        }
    }
    
    /**
     * 生成订单号
     */
    private String generateOrderId() {
        return "VIP" + System.currentTimeMillis() + 
               String.format("%04d", new Random().nextInt(10000));
    }
    
    /**
     * 计算折扣金额
     */
    private BigDecimal calculateDiscount(BigDecimal originalAmount, Coupon coupon) {
        switch (coupon.getDiscountType()) {
            case PERCENTAGE:
                return originalAmount.multiply(coupon.getDiscountValue())
                    .divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            case FIXED_AMOUNT:
                return coupon.getDiscountValue().min(originalAmount);
            default:
                return BigDecimal.ZERO;
        }
    }
}

## 4. 自动续费管理

### 4.1 续费任务调度器

```java
/**
 * VIP自动续费调度器
 */
@Component
@EnableScheduling
public class VipRenewalScheduler {

    @Autowired
    private VipSubscriptionService subscriptionService;

    @Autowired
    private VipRenewalService renewalService;

    @Autowired
    private NotificationService notificationService;

    /**
     * 每日检查即将到期的订阅
     */
    @Scheduled(cron = "0 0 9 * * ?") // 每天上午9点执行
    public void checkExpiringSubscriptions() {
        log.info("开始检查即将到期的VIP订阅");

        try {
            // 1. 查询7天内到期的订阅
            LocalDateTime sevenDaysLater = LocalDateTime.now().plusDays(7);
            List<UserVipSubscription> expiringSubscriptions =
                subscriptionService.getExpiringSubscriptions(sevenDaysLater);

            for (UserVipSubscription subscription : expiringSubscriptions) {
                handleExpiringSubscription(subscription);
            }

            log.info("即将到期订阅检查完成，处理数量: {}", expiringSubscriptions.size());

        } catch (Exception e) {
            log.error("检查即将到期订阅失败", e);
        }
    }

    /**
     * 每日执行自动续费
     */
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void processAutoRenewals() {
        log.info("开始处理自动续费");

        try {
            // 1. 查询今日到期且开启自动续费的订阅
            LocalDate today = LocalDate.now();
            List<UserVipSubscription> autoRenewSubscriptions =
                subscriptionService.getAutoRenewSubscriptions(today);

            for (UserVipSubscription subscription : autoRenewSubscriptions) {
                processAutoRenewal(subscription);
            }

            log.info("自动续费处理完成，处理数量: {}", autoRenewSubscriptions.size());

        } catch (Exception e) {
            log.error("处理自动续费失败", e);
        }
    }

    /**
     * 每日检查已过期订阅
     */
    @Scheduled(cron = "0 0 1 * * ?") // 每天凌晨1点执行
    public void processExpiredSubscriptions() {
        log.info("开始处理已过期订阅");

        try {
            // 1. 查询昨日过期的订阅
            LocalDate yesterday = LocalDate.now().minusDays(1);
            List<UserVipSubscription> expiredSubscriptions =
                subscriptionService.getExpiredSubscriptions(yesterday);

            for (UserVipSubscription subscription : expiredSubscriptions) {
                processExpiredSubscription(subscription);
            }

            log.info("过期订阅处理完成，处理数量: {}", expiredSubscriptions.size());

        } catch (Exception e) {
            log.error("处理过期订阅失败", e);
        }
    }

    /**
     * 处理即将到期的订阅
     */
    private void handleExpiringSubscription(UserVipSubscription subscription) {
        try {
            long daysUntilExpiry = ChronoUnit.DAYS.between(
                LocalDateTime.now(), subscription.getExpiresAt());

            if (daysUntilExpiry == 7) {
                // 7天提醒
                notificationService.sendRenewalReminder(subscription.getUserId(),
                    ReminderType.SEVEN_DAYS, subscription);
            } else if (daysUntilExpiry == 3) {
                // 3天提醒
                notificationService.sendRenewalReminder(subscription.getUserId(),
                    ReminderType.THREE_DAYS, subscription);
            } else if (daysUntilExpiry == 1) {
                // 1天提醒
                notificationService.sendRenewalReminder(subscription.getUserId(),
                    ReminderType.ONE_DAY, subscription);
            }

        } catch (Exception e) {
            log.error("处理即将到期订阅失败, subscriptionId: {}", subscription.getId(), e);
        }
    }

    /**
     * 处理自动续费
     */
    private void processAutoRenewal(UserVipSubscription subscription) {
        try {
            if (!subscription.getAutoRenew()) {
                return;
            }

            // 执行自动续费
            AutoRenewalResult result = renewalService.executeAutoRenewal(subscription);

            if (result.isSuccess()) {
                log.info("自动续费成功, userId: {}, subscriptionId: {}",
                    subscription.getUserId(), subscription.getId());

                // 发送续费成功通知
                notificationService.sendRenewalSuccessNotification(
                    subscription.getUserId(), result);
            } else {
                log.warn("自动续费失败, userId: {}, reason: {}",
                    subscription.getUserId(), result.getFailureReason());

                // 发送续费失败通知
                notificationService.sendRenewalFailureNotification(
                    subscription.getUserId(), result);

                // 关闭自动续费
                subscriptionService.disableAutoRenewal(subscription.getId());
            }

        } catch (Exception e) {
            log.error("处理自动续费失败, subscriptionId: {}", subscription.getId(), e);
        }
    }

    /**
     * 处理已过期订阅
     */
    private void processExpiredSubscription(UserVipSubscription subscription) {
        try {
            // 1. 更新订阅状态为过期
            subscription.setStatus(SubscriptionStatus.EXPIRED);
            subscription.setUpdatedAt(LocalDateTime.now());
            subscriptionService.updateSubscription(subscription);

            // 2. 执行降级处理
            subscriptionService.handleSubscriptionDowngrade(subscription);

            // 3. 发送过期通知
            notificationService.sendSubscriptionExpiredNotification(
                subscription.getUserId(), subscription);

            // 4. 发布过期事件
            VipSubscriptionExpiredEvent event = VipSubscriptionExpiredEvent.builder()
                .userId(subscription.getUserId())
                .previousTier(subscription.getUserTier())
                .planId(subscription.getPlanId())
                .expiredAt(subscription.getExpiresAt())
                .build();

            eventPublisher.publishEvent(event);

            log.info("订阅过期处理完成, userId: {}, tier: {}",
                subscription.getUserId(), subscription.getUserTier());

        } catch (Exception e) {
            log.error("处理过期订阅失败, subscriptionId: {}", subscription.getId(), e);
        }
    }
}
```

### 4.2 自动续费服务

```java
/**
 * VIP自动续费服务
 */
@Service
public class VipRenewalService {

    @Autowired
    private PaymentService paymentService;

    @Autowired
    private VipOrderService orderService;

    @Autowired
    private VipSubscriptionService subscriptionService;

    /**
     * 执行自动续费
     */
    public AutoRenewalResult executeAutoRenewal(UserVipSubscription subscription) {
        try {
            // 1. 获取续费套餐（通常是相同套餐）
            VipPlan renewalPlan = vipPlanService.getPlanById(subscription.getPlanId());
            if (renewalPlan == null || !renewalPlan.getIsActive()) {
                return AutoRenewalResult.failure("续费套餐不可用");
            }

            // 2. 创建续费订单
            VipOrder renewalOrder = orderService.createOrder(
                subscription.getUserId(), renewalPlan, OrderType.AUTO_RENEW);

            // 3. 执行支付
            PaymentRequest paymentRequest = PaymentRequest.builder()
                .orderId(renewalOrder.getOrderId())
                .userId(subscription.getUserId())
                .amount(renewalOrder.getAmount())
                .currency(renewalOrder.getCurrency())
                .paymentMethod(subscription.getPaymentMethod())
                .description("VIP自动续费")
                .build();

            PaymentResult paymentResult = paymentService.processPayment(paymentRequest);

            if (paymentResult.isSuccess()) {
                // 4. 支付成功，激活续费
                subscriptionService.activateRenewal(renewalOrder.getOrderId());

                return AutoRenewalResult.success(renewalOrder, paymentResult);
            } else {
                // 5. 支付失败
                orderService.markOrderAsFailed(renewalOrder.getOrderId(),
                    paymentResult.getErrorMessage());

                return AutoRenewalResult.failure("支付失败: " + paymentResult.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("执行自动续费失败, subscriptionId: {}", subscription.getId(), e);
            return AutoRenewalResult.failure("系统错误: " + e.getMessage());
        }
    }

    /**
     * 重试失败的自动续费
     */
    public void retryFailedRenewals() {
        try {
            // 查询24小时内失败的自动续费订单
            LocalDateTime yesterday = LocalDateTime.now().minusDays(1);
            List<VipOrder> failedOrders = orderService.getFailedAutoRenewOrders(yesterday);

            for (VipOrder order : failedOrders) {
                // 最多重试3次
                if (order.getRetryCount() < 3) {
                    retryAutoRenewal(order);
                }
            }

        } catch (Exception e) {
            log.error("重试失败的自动续费异常", e);
        }
    }

    private void retryAutoRenewal(VipOrder order) {
        try {
            // 重新执行支付
            PaymentRequest retryRequest = PaymentRequest.builder()
                .orderId(order.getOrderId())
                .userId(order.getUserId())
                .amount(order.getAmount())
                .currency(order.getCurrency())
                .description("VIP自动续费重试")
                .build();

            PaymentResult result = paymentService.processPayment(retryRequest);

            if (result.isSuccess()) {
                subscriptionService.activateRenewal(order.getOrderId());
                log.info("自动续费重试成功, orderId: {}", order.getOrderId());
            } else {
                orderService.incrementRetryCount(order.getOrderId());
                log.warn("自动续费重试失败, orderId: {}, reason: {}",
                    order.getOrderId(), result.getErrorMessage());
            }

        } catch (Exception e) {
            log.error("重试自动续费失败, orderId: {}", order.getOrderId(), e);
        }
    }
}

/**
 * 自动续费结果
 */
@Data
@Builder
public class AutoRenewalResult {
    private boolean success;
    private String failureReason;
    private VipOrder order;
    private PaymentResult paymentResult;
    private LocalDateTime renewedUntil;

    public static AutoRenewalResult success(VipOrder order, PaymentResult paymentResult) {
        return AutoRenewalResult.builder()
            .success(true)
            .order(order)
            .paymentResult(paymentResult)
            .build();
    }

    public static AutoRenewalResult failure(String reason) {
        return AutoRenewalResult.builder()
            .success(false)
            .failureReason(reason)
            .build();
    }
}
```
```
