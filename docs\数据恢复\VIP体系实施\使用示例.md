# XPrinter数据恢复VIP权限功能使用示例

## 📱 前端集成示例

### 1. 获取用户权限信息

```javascript
// 获取用户数据恢复权限
async function getUserRecoveryPermission() {
    try {
        const response = await fetch('/api/v2/datarecovery/getRecoveryPermission', {
            method: 'GET',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            const permission = result.data;
            
            // 显示用户等级和权限信息
            displayUserPermission(permission);
            
            return permission;
        } else {
            console.error('获取权限信息失败:', result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 显示权限信息的UI组件
function displayUserPermission(permission) {
    const permissionInfo = document.getElementById('permission-info');
    
    permissionInfo.innerHTML = `
        <div class="user-tier">
            <span class="tier-badge ${permission.userTier.toLowerCase()}">
                ${permission.userTierDisplay}
            </span>
        </div>
        <div class="recovery-period">
            <i class="icon-clock"></i>
            可恢复 ${permission.recoveryPeriodDays} 天内的删除数据
        </div>
        ${permission.upgradeMessage ? `
            <div class="upgrade-tip">
                <i class="icon-info"></i>
                ${permission.upgradeMessage}
                <button class="btn-upgrade" onclick="showUpgradeDialog()">立即升级</button>
            </div>
        ` : ''}
    `;
}
```

### 2. 查询已删除数据

```javascript
// 查询已删除数据（带权限信息）
async function getDeletedData(dataType, pageNumber = 1, pageSize = 10) {
    try {
        const response = await fetch(
            `/api/v2/datarecovery/getDeletedData?dataType=${dataType}&pageNumber=${pageNumber}&pageSize=${pageSize}`,
            {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${userToken}`,
                    'Content-Type': 'application/json'
                }
            }
        );
        
        const result = await response.json();
        
        if (result.success) {
            const { page, permission } = result.data;
            
            // 显示数据列表
            displayDeletedDataList(page.list);
            
            // 显示权限提示
            displayPermissionTip(permission);
            
            return result.data;
        } else {
            console.error('查询失败:', result.msg);
        }
    } catch (error) {
        console.error('请求失败:', error);
    }
}

// 显示权限提示
function displayPermissionTip(permission) {
    const tipContainer = document.getElementById('permission-tip');
    
    if (permission.userTier === 'FREE') {
        tipContainer.innerHTML = `
            <div class="permission-tip free-user">
                <i class="icon-info"></i>
                您当前是免费用户，只能查看和恢复 ${permission.recoveryPeriodDays} 天内删除的数据。
                <a href="#" onclick="showUpgradeDialog()">升级VIP</a> 可恢复6个月内的数据。
            </div>
        `;
    } else {
        tipContainer.innerHTML = `
            <div class="permission-tip vip-user">
                <i class="icon-vip"></i>
                VIP用户特权：可恢复 ${permission.recoveryPeriodDays} 天内删除的数据
            </div>
        `;
    }
}
```

### 3. 恢复数据操作

```javascript
// 恢复单个数据
async function recoverSingleData(dataType, dataId) {
    try {
        const response = await fetch('/api/v2/datarecovery/recoverSingleData', {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${userToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                dataType: dataType,
                dataId: dataId
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccessMessage('数据恢复成功！');
            // 刷新数据列表
            refreshDeletedDataList();
        } else {
            // 处理权限不足的情况
            if (result.msg.includes('超出可恢复时间范围')) {
                showUpgradeDialog(result.msg);
            } else {
                showErrorMessage(result.msg);
            }
        }
    } catch (error) {
        console.error('恢复失败:', error);
        showErrorMessage('恢复操作失败，请稍后重试');
    }
}

// 显示升级对话框
function showUpgradeDialog(message = null) {
    const dialog = document.getElementById('upgrade-dialog');
    const messageElement = dialog.querySelector('.upgrade-message');
    
    if (message) {
        messageElement.textContent = message;
    } else {
        messageElement.textContent = '升级VIP可享受更长的数据恢复期限和更多高级功能';
    }
    
    dialog.style.display = 'block';
}
```

## 🎨 CSS样式示例

```css
/* 用户等级徽章 */
.tier-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    text-transform: uppercase;
}

.tier-badge.free {
    background-color: #f0f0f0;
    color: #666;
}

.tier-badge.vip_monthly,
.tier-badge.vip_yearly,
.tier-badge.vip_lifetime {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    color: #333;
    box-shadow: 0 2px 4px rgba(255, 215, 0, 0.3);
}

/* 权限提示 */
.permission-tip {
    padding: 12px 16px;
    border-radius: 8px;
    margin: 16px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.permission-tip.free-user {
    background-color: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.permission-tip.vip-user {
    background-color: #fff8e1;
    border: 1px solid #ffd700;
    color: #f57f17;
}

/* 升级按钮 */
.btn-upgrade {
    background: linear-gradient(45deg, #ff6b6b, #ee5a24);
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    cursor: pointer;
    font-weight: bold;
    transition: transform 0.2s;
}

.btn-upgrade:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(238, 90, 36, 0.3);
}

/* 升级对话框 */
.upgrade-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.upgrade-dialog-content {
    background: white;
    padding: 32px;
    border-radius: 12px;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}
```

## 📱 React组件示例

```jsx
import React, { useState, useEffect } from 'react';

// 数据恢复权限组件
const DataRecoveryPermission = () => {
    const [permission, setPermission] = useState(null);
    const [loading, setLoading] = useState(true);

    useEffect(() => {
        fetchPermission();
    }, []);

    const fetchPermission = async () => {
        try {
            const response = await fetch('/api/v2/datarecovery/getRecoveryPermission');
            const result = await response.json();
            
            if (result.success) {
                setPermission(result.data);
            }
        } catch (error) {
            console.error('获取权限失败:', error);
        } finally {
            setLoading(false);
        }
    };

    if (loading) {
        return <div className="loading">加载中...</div>;
    }

    if (!permission) {
        return <div className="error">获取权限信息失败</div>;
    }

    return (
        <div className="permission-card">
            <div className="user-tier">
                <span className={`tier-badge ${permission.userTier.toLowerCase()}`}>
                    {permission.userTierDisplay}
                </span>
            </div>
            
            <div className="recovery-info">
                <h3>数据恢复权限</h3>
                <p>可恢复 {permission.recoveryPeriodDays} 天内删除的数据</p>
                <p className="earliest-date">
                    最早可恢复时间：{new Date(permission.earliestRecoveryDate).toLocaleDateString()}
                </p>
            </div>
            
            {permission.upgradeMessage && (
                <div className="upgrade-section">
                    <p className="upgrade-message">{permission.upgradeMessage}</p>
                    <button 
                        className="btn-upgrade"
                        onClick={() => window.open('/vip/upgrade', '_blank')}
                    >
                        立即升级VIP
                    </button>
                </div>
            )}
        </div>
    );
};

// 已删除数据列表组件
const DeletedDataList = ({ dataType }) => {
    const [data, setData] = useState([]);
    const [permission, setPermission] = useState(null);
    const [currentPage, setCurrentPage] = useState(1);

    useEffect(() => {
        fetchDeletedData();
    }, [dataType, currentPage]);

    const fetchDeletedData = async () => {
        try {
            const response = await fetch(
                `/api/v2/datarecovery/getDeletedData?dataType=${dataType}&pageNumber=${currentPage}&pageSize=10`
            );
            const result = await response.json();
            
            if (result.success) {
                setData(result.data.page.list);
                setPermission(result.data.permission);
            }
        } catch (error) {
            console.error('查询失败:', error);
        }
    };

    const handleRecover = async (itemId) => {
        try {
            const response = await fetch('/api/v2/datarecovery/recoverSingleData', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    dataType: dataType,
                    dataId: itemId
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                alert('恢复成功！');
                fetchDeletedData(); // 刷新列表
            } else {
                if (result.msg.includes('超出可恢复时间范围')) {
                    if (confirm(result.msg + '\n\n是否立即升级VIP？')) {
                        window.open('/vip/upgrade', '_blank');
                    }
                } else {
                    alert(result.msg);
                }
            }
        } catch (error) {
            console.error('恢复失败:', error);
            alert('恢复失败，请稍后重试');
        }
    };

    return (
        <div className="deleted-data-list">
            {permission && (
                <div className={`permission-tip ${permission.userTier === 'FREE' ? 'free-user' : 'vip-user'}`}>
                    {permission.userTier === 'FREE' ? (
                        <>
                            <span>免费用户只能恢复 {permission.recoveryPeriodDays} 天内的数据</span>
                            <button onClick={() => window.open('/vip/upgrade', '_blank')}>
                                升级VIP
                            </button>
                        </>
                    ) : (
                        <span>VIP特权：可恢复 {permission.recoveryPeriodDays} 天内的数据</span>
                    )}
                </div>
            )}
            
            <div className="data-items">
                {data.map(item => (
                    <div key={item.id} className="data-item">
                        <div className="item-info">
                            <h4>{item.name}</h4>
                            <p>删除时间：{new Date(item.deleteTime).toLocaleString()}</p>
                        </div>
                        <button 
                            className="btn-recover"
                            onClick={() => handleRecover(item.id)}
                        >
                            恢复
                        </button>
                    </div>
                ))}
            </div>
        </div>
    );
};

export { DataRecoveryPermission, DeletedDataList };
```

## 🔧 后端使用示例

```java
// 在其他服务中使用VIP权限检查
@Service
public class TemplateService {
    
    @Autowired
    private VipPermissionService vipPermissionService;
    
    /**
     * 删除模板时的权限提示
     */
    public RetKit deleteTemplate(Integer userId, Integer templateId) {
        // 执行删除操作
        RetKit deleteResult = performDelete(templateId);
        
        if (deleteResult.success()) {
            // 获取用户权限信息，提供恢复提示
            DataRecoveryPermissionResult permission = 
                vipPermissionService.checkDataRecoveryPermission(userId);
            
            Map<String, Object> result = new HashMap<>();
            result.put("deleted", true);
            result.put("recoveryInfo", buildRecoveryInfo(permission));
            
            return RetKit.ok(result);
        }
        
        return deleteResult;
    }
    
    private Map<String, Object> buildRecoveryInfo(DataRecoveryPermissionResult permission) {
        Map<String, Object> info = new HashMap<>();
        info.put("canRecover", permission.isHasPermission());
        info.put("recoveryPeriodDays", permission.getRecoveryPeriodDays());
        info.put("message", String.format("数据已删除，%d天内可在回收站恢复", 
            permission.getRecoveryPeriodDays()));
        
        if (permission.getUpgradeMessage() != null) {
            info.put("upgradeMessage", permission.getUpgradeMessage());
        }
        
        return info;
    }
}
```

这些示例展示了如何在前端和后端代码中集成新的VIP权限功能，提供了完整的用户体验流程。
