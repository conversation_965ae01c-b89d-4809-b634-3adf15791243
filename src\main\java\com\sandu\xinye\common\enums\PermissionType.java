package com.sandu.xinye.common.enums;

/**
 * 权限类型枚举
 * 定义系统中各种功能权限和配额权限
 */
public enum PermissionType {
    // 数据恢复相关权限
    FEATURE_DATA_RECOVERY("数据恢复功能", PermissionCategory.FEATURE, "data_recovery"),
    QUOTA_BATCH_RECOVERY_DAILY("每日批量恢复次数", PermissionCategory.QUOTA, "batch_recovery_daily"),
    
    // OCR识图相关权限
    FEATURE_OCR_CREATE("识图新建功能", PermissionCategory.FEATURE, "ocr_create"),
    QUOTA_OCR_MONTHLY("月度OCR次数", PermissionCategory.QUOTA, "ocr_monthly"),
    
    // 存储相关权限
    QUOTA_TEMPLATE_COUNT("模板数量限制", PermissionCategory.QUOTA, "template_count"),
    QUOTA_STORAGE_SIZE("存储空间限制", PermissionCategory.QUOTA, "storage_size"),
    QUOTA_FILE_SIZE_LIMIT("单文件大小限制", PermissionCategory.QUOTA, "file_size_limit"),
    
    // 团队协作权限
    FEATURE_TEAM_COLLABORATION("团队协作功能", PermissionCategory.FEATURE, "team_collaboration"),
    QUOTA_TEAM_MEMBERS("团队成员数量", PermissionCategory.QUOTA, "team_members"),
    
    // 体验相关权限
    EXPERIENCE_AD_FREE("无广告体验", PermissionCategory.EXPERIENCE, "ad_free"),
    EXPERIENCE_PRIORITY_SUPPORT("优先客服", PermissionCategory.EXPERIENCE, "priority_support"),
    EXPERIENCE_BETA_FEATURES("Beta功能体验", PermissionCategory.EXPERIENCE, "beta_features");
    
    private final String displayName;
    private final PermissionCategory category;
    private final String code;
    
    PermissionType(String displayName, PermissionCategory category, String code) {
        this.displayName = displayName;
        this.category = category;
        this.code = code;
    }
    
    /**
     * 获取显示名称
     */
    public String getDisplayName() {
        return displayName;
    }
    
    /**
     * 获取权限分类
     */
    public PermissionCategory getCategory() {
        return category;
    }
    
    /**
     * 获取权限代码
     */
    public String getCode() {
        return code;
    }
    
    /**
     * 权限分类枚举
     */
    public enum PermissionCategory {
        FEATURE("功能权限"),
        QUOTA("配额权限"),
        EXPERIENCE("体验权限");
        
        private final String displayName;
        
        PermissionCategory(String displayName) {
            this.displayName = displayName;
        }
        
        public String getDisplayName() {
            return displayName;
        }
    }
}
